const http = require("http");
const fs = require("fs");
const url = require("url");
const path = require("path");
const querystring = require("querystring");

// 数据文件路径
const dataFilePath = path.join(__dirname, "src", "data.json");
const usersFilePath = path.join(__dirname, "src", "users.json");

// 简单的内存会话存储
const sessions = new Map();

const server = http.createServer(function (req, res) {
    let pathname = url.parse(req.url).pathname;
    let method = req.method;

    // 设置CORS头
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
    res.setHeader("Access-Control-Allow-Headers", "Content-Type");

    // 处理预检请求
    if (method === "OPTIONS") {
        res.writeHead(200);
        res.end();
        return;
    }

    // 根路径重定向到首页
    if (pathname === "/") {
        pathname = "/index.html";
    }

    // API路由
    if (pathname.startsWith("/api/")) {
        handleApiRequest(req, res, pathname, method);
        return;
    }

    // 处理HTML页面路由
    if (pathname === "/index.html") {
        serveHtmlFile(res, "./src/index.html");
    } else if (pathname === "/add.html") {
        serveHtmlFile(res, "./src/add.html");
    } else if (pathname === "/delete.html") {
        serveHtmlFile(res, "./src/delete.html");
    } else if (pathname === "/edit.html") {
        serveHtmlFile(res, "./src/edit.html");
    } else if (pathname === "/search.html") {
        serveHtmlFile(res, "./src/search.html");
    } else if (pathname === "/login.html") {
        serveHtmlFile(res, "./src/login.html");
    } else if (pathname === "/user/add") {
        res.writeHead(200, {
            "content-type": "text/html",
        });
        res.write("user/add");
        res.end();
    } else {
        // 尝试提供静态文件
        const filePath = path.join(__dirname, "src", pathname);

        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                // 文件不存在，返回404
                res.writeHead(404, {
                    "content-type": "text/html",
                });
                res.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>404 - 页面未找到</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              h1 { color: #e74c3c; }
              a { color: #3498db; text-decoration: none; }
              a:hover { text-decoration: underline; }
            </style>
          </head>
          <body>
            <h1>404 - 页面未找到</h1>
            <p>抱歉，您访问的页面不存在。</p>
            <p><a href="/">返回首页</a></p>
          </body>
          </html>
        `);
                res.end();
            } else {
                // 文件存在，提供静态文件服务
                serveStaticFile(res, filePath);
            }
        });
    }
});

// 提供HTML文件的函数
function serveHtmlFile(res, filePath) {
    fs.readFile(filePath, "utf8", (err, data) => {
        if (err) {
            res.writeHead(500, {
                "content-type": "text/html",
            });
            res.write("Internal Server Error");
            res.end();
            return;
        }

        res.writeHead(200, {
            "content-type": "text/html; charset=utf-8",
        });
        res.write(data);
        res.end();
    });
}

// 提供静态文件的函数
function serveStaticFile(res, filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        ".html": "text/html",
        ".js": "text/javascript",
        ".css": "text/css",
        ".json": "application/json",
        ".png": "image/png",
        ".jpg": "image/jpg",
        ".gif": "image/gif",
        ".svg": "image/svg+xml",
        ".wav": "audio/wav",
        ".mp4": "video/mp4",
        ".woff": "application/font-woff",
        ".ttf": "application/font-ttf",
        ".eot": "application/vnd.ms-fontobject",
        ".otf": "application/font-otf",
        ".wasm": "application/wasm",
    };

    const contentType = mimeTypes[ext] || "application/octet-stream";

    fs.readFile(filePath, (err, content) => {
        if (err) {
            res.writeHead(500);
            res.end(
                "Sorry, check with the site admin for error: " +
                    err.code +
                    " ..\n"
            );
        } else {
            res.writeHead(200, { "Content-Type": contentType });
            res.end(content, "utf-8");
        }
    });
}

// API处理函数
function handleApiRequest(req, res, pathname, method) {
    // 认证相关API
    if (pathname === "/api/auth/login" && method === "POST") {
        handleLogin(req, res);
    } else if (pathname === "/api/auth/logout" && method === "POST") {
        handleLogout(req, res);
    } else if (pathname === "/api/auth/verify" && method === "POST") {
        handleVerifyToken(req, res);
    } else if (pathname === "/api/data" && method === "GET") {
        // 获取所有数据
        getData(res);
    } else if (pathname === "/api/data" && method === "POST") {
        // 添加数据
        addData(req, res);
    } else if (pathname.startsWith("/api/data/") && method === "PUT") {
        // 更新数据
        const id = parseInt(pathname.split("/")[3]);
        updateData(req, res, id);
    } else if (pathname.startsWith("/api/data/") && method === "DELETE") {
        // 删除数据
        const id = parseInt(pathname.split("/")[3]);
        deleteData(res, id);
    } else if (pathname === "/api/search" && method === "GET") {
        // 搜索数据
        searchData(req, res);
    } else {
        res.writeHead(404, { "Content-Type": "application/json" });
        res.end(JSON.stringify({ error: "API endpoint not found" }));
    }
}

// 读取数据文件
function readDataFile() {
    try {
        const data = fs.readFileSync(dataFilePath, "utf8");
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

// 写入数据文件
function writeDataFile(data) {
    fs.writeFileSync(dataFilePath, JSON.stringify(data, null, 2));
}

// 读取用户文件
function readUsersFile() {
    try {
        const data = fs.readFileSync(usersFilePath, "utf8");
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

// 写入用户文件
function writeUsersFile(users) {
    fs.writeFileSync(usersFilePath, JSON.stringify(users, null, 2));
}

// 生成简单的token
function generateToken(user) {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2);
    return `${user.id}_${timestamp}_${randomStr}`;
}

// 验证token
function verifyToken(token) {
    if (!token) return null;

    // 从sessions中查找token
    for (let [sessionToken, sessionData] of sessions) {
        if (sessionToken === token) {
            // 检查token是否过期（24小时）
            const now = Date.now();
            const tokenAge = now - sessionData.createdAt;
            if (tokenAge > 24 * 60 * 60 * 1000) {
                sessions.delete(sessionToken);
                return null;
            }
            return sessionData.user;
        }
    }
    return null;
}

// 从请求头获取token
function getTokenFromRequest(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }
    return null;
}

// 处理登录请求
function handleLogin(req, res) {
    let body = "";
    req.on("data", (chunk) => {
        body += chunk.toString();
    });

    req.on("end", () => {
        try {
            const { username, password, rememberMe } = JSON.parse(body);

            if (!username || !password) {
                res.writeHead(400, { "Content-Type": "application/json" });
                res.end(JSON.stringify({
                    success: false,
                    message: "用户名和密码不能为空"
                }));
                return;
            }

            const users = readUsersFile();
            const user = users.find(u => u.username === username && u.password === password);

            if (user) {
                // 登录成功
                const token = generateToken(user);

                // 存储会话
                sessions.set(token, {
                    user: {
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        role: user.role
                    },
                    createdAt: Date.now(),
                    rememberMe: rememberMe
                });

                // 更新最后登录时间
                user.lastLogin = new Date().toISOString().split("T")[0];
                writeUsersFile(users);

                res.writeHead(200, { "Content-Type": "application/json" });
                res.end(JSON.stringify({
                    success: true,
                    message: "登录成功",
                    token: token,
                    user: {
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        role: user.role
                    }
                }));
            } else {
                // 登录失败
                res.writeHead(401, { "Content-Type": "application/json" });
                res.end(JSON.stringify({
                    success: false,
                    message: "用户名或密码错误"
                }));
            }
        } catch (error) {
            res.writeHead(400, { "Content-Type": "application/json" });
            res.end(JSON.stringify({
                success: false,
                message: "请求数据格式错误"
            }));
        }
    });
}

// 处理登出请求
function handleLogout(req, res) {
    const token = getTokenFromRequest(req);

    if (token && sessions.has(token)) {
        sessions.delete(token);
    }

    res.writeHead(200, { "Content-Type": "application/json" });
    res.end(JSON.stringify({
        success: true,
        message: "登出成功"
    }));
}

// 处理token验证请求
function handleVerifyToken(req, res) {
    const token = getTokenFromRequest(req);
    const user = verifyToken(token);

    if (user) {
        res.writeHead(200, { "Content-Type": "application/json" });
        res.end(JSON.stringify({
            success: true,
            user: user
        }));
    } else {
        res.writeHead(401, { "Content-Type": "application/json" });
        res.end(JSON.stringify({
            success: false,
            message: "Token无效或已过期"
        }));
    }
}

// 获取所有数据
function getData(res) {
    const data = readDataFile();
    res.writeHead(200, { "Content-Type": "application/json" });
    res.end(JSON.stringify(data));
}

// 添加数据
function addData(req, res) {
    let body = "";
    req.on("data", (chunk) => {
        body += chunk.toString();
    });

    req.on("end", () => {
        try {
            const newItem = JSON.parse(body);
            const data = readDataFile();

            // 生成新ID
            const maxId =
                data.length > 0
                    ? Math.max(...data.map((item) => item.id))
                    : 1000;
            newItem.id = maxId + 1;
            newItem.createTime = new Date().toISOString().split("T")[0];

            data.push(newItem);
            writeDataFile(data);

            res.writeHead(201, { "Content-Type": "application/json" });
            res.end(JSON.stringify(newItem));
        } catch (error) {
            res.writeHead(400, { "Content-Type": "application/json" });
            res.end(JSON.stringify({ error: "Invalid JSON" }));
        }
    });
}

// 更新数据
function updateData(req, res, id) {
    let body = "";
    req.on("data", (chunk) => {
        body += chunk.toString();
    });

    req.on("end", () => {
        try {
            const updatedItem = JSON.parse(body);
            const data = readDataFile();
            const index = data.findIndex((item) => item.id === id);

            if (index === -1) {
                res.writeHead(404, { "Content-Type": "application/json" });
                res.end(JSON.stringify({ error: "Item not found" }));
                return;
            }

            data[index] = { ...data[index], ...updatedItem, id: id };
            writeDataFile(data);

            res.writeHead(200, { "Content-Type": "application/json" });
            res.end(JSON.stringify(data[index]));
        } catch (error) {
            res.writeHead(400, { "Content-Type": "application/json" });
            res.end(JSON.stringify({ error: "Invalid JSON" }));
        }
    });
}

// 删除数据
function deleteData(res, id) {
    const data = readDataFile();
    const index = data.findIndex((item) => item.id === id);

    if (index === -1) {
        res.writeHead(404, { "Content-Type": "application/json" });
        res.end(JSON.stringify({ error: "Item not found" }));
        return;
    }

    data.splice(index, 1);
    writeDataFile(data);

    res.writeHead(200, { "Content-Type": "application/json" });
    res.end(JSON.stringify({ message: "Item deleted successfully" }));
}

// 搜索数据
function searchData(req, res) {
    const urlParts = url.parse(req.url, true);
    const query = urlParts.query;
    const data = readDataFile();

    let filteredData = data;

    // 按关键词搜索
    if (query.keyword) {
        filteredData = filteredData.filter(
            (item) =>
                item.name.toLowerCase().includes(query.keyword.toLowerCase()) ||
                (item.description &&
                    item.description
                        .toLowerCase()
                        .includes(query.keyword.toLowerCase()))
        );
    }

    // 按类别筛选
    if (query.category && query.category !== "") {
        filteredData = filteredData.filter(
            (item) => item.category === query.category
        );
    }

    // 按状态筛选
    if (query.status && query.status !== "") {
        filteredData = filteredData.filter(
            (item) => item.status === query.status
        );
    }

    res.writeHead(200, { "Content-Type": "application/json" });
    res.end(JSON.stringify(filteredData));
}

server.listen(3000, function () {
    console.log("Server is listening on port 3000");
    console.log("Visit: http://localhost:3000");
    console.log("Alternative: http://127.0.0.1:3000");
});
