<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>首页 - 多页面管理系统</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background-color: #f5f7fa;
                color: #333;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            header {
                background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                color: white;
                padding: 20px 0;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                margin-bottom: 30px;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }

            .logo {
                font-size: 28px;
                font-weight: bold;
            }

            nav ul {
                display: flex;
                list-style: none;
            }

            nav li {
                margin-left: 20px;
            }

            nav a {
                color: white;
                text-decoration: none;
                padding: 8px 15px;
                border-radius: 5px;
                transition: background-color 0.3s;
            }

            nav a:hover,
            nav a.active {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .welcome-section {
                text-align: center;
                padding: 40px 0;
            }

            .welcome-section h2 {
                font-size: 32px;
                margin-bottom: 20px;
                color: #2c3e50;
            }

            .welcome-section p {
                font-size: 18px;
                max-width: 700px;
                margin: 0 auto 30px;
                color: #7f8c8d;
            }

            button {
                background: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #2980b9;
            }

            .feature-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-top: 40px;
            }

            .feature-card {
                background: white;
                border-radius: 10px;
                padding: 25px;
                text-align: center;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                transition: transform 0.3s, box-shadow 0.3s;
            }

            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
            }

            .feature-card i {
                font-size: 40px;
                margin-bottom: 15px;
                color: #3498db;
            }

            .feature-card h3 {
                margin-bottom: 15px;
                color: #2c3e50;
            }

            .stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 40px;
            }

            .stat-card {
                background: white;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            }

            .stat-card h3 {
                font-size: 32px;
                color: #3498db;
                margin-bottom: 10px;
            }

            .stat-card p {
                color: #7f8c8d;
            }

            footer {
                text-align: center;
                margin-top: 50px;
                padding: 20px;
                color: #7f8c8d;
                border-top: 1px solid #eee;
            }

            /* 数据展示区域样式 */
            .data-section {
                margin-top: 40px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
                overflow: hidden;
            }

            .section-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .section-header h2 {
                margin: 0;
                color: white;
                border: none;
                padding: 0;
            }

            .section-controls {
                display: flex;
                gap: 15px;
                align-items: center;
            }

            .section-controls button {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 8px 15px;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            .section-controls button:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .section-controls select {
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
            }

            .data-table-container {
                overflow-x: auto;
                max-height: 500px;
                overflow-y: auto;
            }

            .data-table {
                width: 100%;
                border-collapse: collapse;
                margin: 0;
            }

            .data-table th,
            .data-table td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #eee;
            }

            .data-table th {
                background-color: #f8f9fa;
                font-weight: 600;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            .data-table tr:hover {
                background-color: #f5f7fa;
            }

            .action-buttons {
                display: flex;
                gap: 8px;
            }

            .action-buttons button {
                padding: 6px 12px;
                font-size: 12px;
                border-radius: 4px;
                border: none;
                cursor: pointer;
                transition: all 0.3s;
            }

            .btn-edit {
                background: #f39c12;
                color: white;
            }

            .btn-edit:hover {
                background: #d35400;
            }

            .btn-delete {
                background: #e74c3c;
                color: white;
            }

            .btn-delete:hover {
                background: #c0392b;
            }

            .btn-view {
                background: #3498db;
                color: white;
            }

            .btn-view:hover {
                background: #2980b9;
            }

            .status-badge {
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
            }

            .status-normal {
                background: #d4edda;
                color: #155724;
            }

            .status-pause {
                background: #fff3cd;
                color: #856404;
            }

            .status-stop {
                background: #f8d7da;
                color: #721c24;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header>
                <div class="header-content">
                    <div class="logo">多页面管理系统</div>
                    <nav>
                        <ul>
                            <li>
                                <a href="/index.html" class="active">首页</a>
                            </li>
                            <li>
                                <a href="/add.html">增加页面</a>
                            </li>
                            <li>
                                <a href="/delete.html">删除页面</a>
                            </li>
                            <li>
                                <a href="/edit.html">修改页面</a>
                            </li>
                            <li>
                                <a href="/search.html">查询页面</a>
                            </li>
                            <li id="auth-nav">
                                <a href="/login.html" id="login-link">登录</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <main>
                <div class="welcome-section">
                    <h2>欢迎使用多页面管理系统</h2>
                    <p>
                        这是一个功能完整的多页面管理系统，包含增加、删除、修改和查询功能，满足您的日常数据管理需求。
                    </p>
                    <button>开始使用</button>
                </div>

                <div class="feature-cards">
                    <div class="feature-card">
                        <i>📊</i>
                        <h3>数据统计</h3>
                        <p>直观展示系统数据概览和关键指标</p>
                    </div>
                    <div class="feature-card">
                        <i>➕</i>
                        <h3>数据添加</h3>
                        <p>快速添加新数据到系统中</p>
                    </div>
                    <div class="feature-card">
                        <i>✏️</i>
                        <h3>数据修改</h3>
                        <p>灵活修改已有数据信息</p>
                    </div>
                    <div class="feature-card">
                        <i>🔍</i>
                        <h3>数据查询</h3>
                        <p>快速查找和筛选系统数据</p>
                    </div>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <h3 id="total-count">0</h3>
                        <p>总数据量</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="today-count">0</h3>
                        <p>今日新增</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="normal-count">0</h3>
                        <p>正常状态</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="pause-count">0</h3>
                        <p>暂停状态</p>
                    </div>
                </div>

                <!-- 数据展示区域 -->
                <div class="data-section">
                    <div class="section-header">
                        <h2>系统数据总览</h2>
                        <div class="section-controls">
                            <button onclick="refreshData()">刷新数据</button>
                            <select
                                id="category-filter"
                                onchange="filterData()"
                            >
                                <option value="">所有类别</option>
                                <option value="product">产品</option>
                                <option value="service">服务</option>
                                <option value="technology">技术</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>

                    <div class="data-table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>类别</th>
                                    <th>价格</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="data-table-body">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>

            <footer>
                <p>© 2023 多页面管理系统 | 设计用于演示多页面切换功能</p>
            </footer>
        </div>

        <script>
            let allData = [];
            let filteredData = [];

            // 页面加载时获取数据统计
            document.addEventListener("DOMContentLoaded", function () {
                checkUserLogin();
                loadAllData();
            });

            // 检查用户登录状态
            function checkUserLogin() {
                const token = localStorage.getItem('authToken');
                const userData = localStorage.getItem('userData');

                if (token && userData) {
                    try {
                        const user = JSON.parse(userData);
                        updateAuthNav(user);

                        // 验证token是否仍然有效
                        fetch('/api/auth/verify', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (!data.success) {
                                // Token无效，清除本地存储
                                localStorage.removeItem('authToken');
                                localStorage.removeItem('userData');
                                updateAuthNav(null);
                            }
                        })
                        .catch(error => {
                            console.log('Token验证失败:', error);
                            localStorage.removeItem('authToken');
                            localStorage.removeItem('userData');
                            updateAuthNav(null);
                        });
                    } catch (error) {
                        console.log('用户数据解析失败:', error);
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('userData');
                        updateAuthNav(null);
                    }
                } else {
                    updateAuthNav(null);
                }
            }

            // 更新认证导航
            function updateAuthNav(user) {
                const authNav = document.getElementById('auth-nav');

                if (user) {
                    authNav.innerHTML = `
                        <span style="color: white; margin-right: 15px;">
                            👤 ${user.username} (${user.role === 'admin' ? '管理员' : '用户'})
                        </span>
                        <a href="#" onclick="logout()" style="color: #ffcccb;">登出</a>
                    `;
                } else {
                    authNav.innerHTML = '<a href="/login.html" id="login-link">登录</a>';
                }
            }

            // 登出功能
            function logout() {
                const token = localStorage.getItem('authToken');

                if (token) {
                    fetch('/api/auth/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('登出响应:', data);
                    })
                    .catch(error => {
                        console.log('登出请求失败:', error);
                    });
                }

                // 清除本地存储
                localStorage.removeItem('authToken');
                localStorage.removeItem('userData');
                localStorage.removeItem('rememberLogin');

                // 更新导航
                updateAuthNav(null);

                alert('已成功登出！');
            }

            // 加载所有数据
            function loadAllData() {
                fetch("/api/data")
                    .then((response) => response.json())
                    .then((data) => {
                        allData = data;
                        filteredData = data;
                        updateStatistics(data);
                        updateDataTable(data);
                    })
                    .catch((error) => {
                        console.error("Error loading data:", error);
                    });
            }

            // 更新统计数据
            function updateStatistics(data) {
                const today = new Date().toISOString().split("T")[0];

                // 总数据量
                document.getElementById("total-count").textContent =
                    data.length;

                // 今日新增
                const todayCount = data.filter(
                    (item) => item.createTime === today
                ).length;
                document.getElementById("today-count").textContent = todayCount;

                // 正常状态
                const normalCount = data.filter(
                    (item) => item.status === "normal"
                ).length;
                document.getElementById("normal-count").textContent =
                    normalCount;

                // 暂停状态
                const pauseCount = data.filter(
                    (item) => item.status === "pause"
                ).length;
                document.getElementById("pause-count").textContent = pauseCount;
            }

            // 更新数据表格
            function updateDataTable(data) {
                const tbody = document.getElementById("data-table-body");
                tbody.innerHTML = "";

                if (data.length === 0) {
                    const row = document.createElement("tr");
                    row.innerHTML =
                        '<td colspan="7" style="text-align: center; padding: 40px;">暂无数据</td>';
                    tbody.appendChild(row);
                    return;
                }

                data.forEach((item) => {
                    const row = document.createElement("tr");
                    row.innerHTML = `
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${getCategoryText(item.category)}</td>
                        <td>¥${item.price.toFixed(2)}</td>
                        <td><span class="status-badge status-${
                            item.status
                        }">${getStatusText(item.status)}</span></td>
                        <td>${item.createTime}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-view" onclick="viewItem(${
                                    item.id
                                })">查看</button>
                                <button class="btn-edit" onclick="editItem(${
                                    item.id
                                })">编辑</button>
                                <button class="btn-delete" onclick="deleteItem(${
                                    item.id
                                })">删除</button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 刷新数据
            function refreshData() {
                loadAllData();
                document.getElementById("category-filter").value = "";
            }

            // 筛选数据
            function filterData() {
                const category =
                    document.getElementById("category-filter").value;

                if (category === "") {
                    filteredData = allData;
                } else {
                    filteredData = allData.filter(
                        (item) => item.category === category
                    );
                }

                updateDataTable(filteredData);
            }

            // 查看项目详情
            function viewItem(id) {
                const item = allData.find((d) => d.id === id);
                if (item) {
                    alert(`项目详情：
名称：${item.name}
类别：${getCategoryText(item.category)}
价格：¥${item.price.toFixed(2)}
状态：${getStatusText(item.status)}
描述：${item.description || "无"}
创建时间：${item.createTime}`);
                }
            }

            // 编辑项目
            function editItem(id) {
                window.location.href = `/edit.html?id=${id}`;
            }

            // 删除项目
            function deleteItem(id) {
                if (confirm("确定要删除这条数据吗？")) {
                    fetch(`/api/data/${id}`, {
                        method: "DELETE",
                    })
                        .then((response) => response.json())
                        .then((data) => {
                            alert("数据删除成功！");
                            loadAllData(); // 重新加载数据
                        })
                        .catch((error) => {
                            console.error("Error deleting item:", error);
                            alert("删除失败，请重试！");
                        });
                }
            }

            // 获取类别中文名称
            function getCategoryText(category) {
                const categoryMap = {
                    product: "产品",
                    service: "服务",
                    technology: "技术",
                    other: "其他",
                };
                return categoryMap[category] || category;
            }

            // 获取状态中文名称
            function getStatusText(status) {
                const statusMap = {
                    normal: "正常",
                    pause: "暂停",
                    stop: "停止",
                };
                return statusMap[status] || status;
            }
        </script>
    </body>
</html>
