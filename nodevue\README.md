# 多页面管理系统

一个基于 Node.js 的多页面管理系统，包含用户认证和完整的增删改查功能。

## 功能特性

- 🔐 用户登录认证系统
- 📊 数据统计展示
- ➕ 数据添加功能
- ✏️ 数据修改功能
- 🔍 数据查询和筛选
- 🗑️ 数据删除功能
- 👤 用户会话管理
- 📱 响应式设计

## 技术栈

- **后端**: Node.js (原生 HTTP 模块)
- **前端**: HTML5 + CSS3 + JavaScript
- **数据存储**: JSON 文件
- **认证**: Token-based 认证
- **构建工具**: Webpack

## 快速开始

### 方法一：使用批处理文件（推荐）

1. 双击 `start.bat` 文件
2. 等待服务器启动
3. 浏览器访问 http://localhost:3000

### 方法二：命令行启动

```bash
# 进入项目目录
cd nodevue

# 启动服务器
node server.js
```

## 登录系统

### 演示账户

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 123456 | 管理员 | 拥有所有权限 |
| user | 123456 | 普通用户 | 基本操作权限 |
| test | test123 | 测试用户 | 测试专用账户 |

### 登录功能

- **登录页面**: http://localhost:3000/login.html
- **自动登录**: 支持"记住我"功能
- **会话管理**: Token-based 认证，24小时有效期
- **安全退出**: 清除本地存储和服务器会话

## 项目结构

```
nodevue/
├── src/                    # 源代码目录
│   ├── index.html         # 首页
│   ├── login.html         # 登录页面
│   ├── add.html           # 添加页面
│   ├── edit.html          # 编辑页面
│   ├── delete.html        # 删除页面
│   ├── search.html        # 查询页面
│   ├── data.json          # 数据文件
│   ├── users.json         # 用户数据文件
│   └── index.js           # 前端 JS
├── server.js              # 服务器文件
├── package.json           # 项目配置
├── webpack.config.js      # Webpack 配置
├── start.bat              # Windows 启动脚本
└── README.md              # 说明文档
```

## API 接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/verify` - 验证Token

### 数据接口
- `GET /api/data` - 获取所有数据
- `POST /api/data` - 添加新数据
- `PUT /api/data/:id` - 更新指定数据
- `DELETE /api/data/:id` - 删除指定数据
- `GET /api/search` - 搜索数据

## 页面说明

- **首页** (`/`) - 系统概览和数据统计，显示登录状态
- **登录页面** (`/login.html`) - 用户登录界面
- **添加页面** (`/add.html`) - 添加新数据
- **编辑页面** (`/edit.html`) - 编辑现有数据
- **删除页面** (`/delete.html`) - 删除数据
- **查询页面** (`/search.html`) - 搜索和筛选数据

## 使用说明

1. **首次使用**：
   - 启动服务器后访问 http://localhost:3000
   - 点击右上角"登录"按钮
   - 使用演示账户登录

2. **登录后**：
   - 首页会显示用户信息和角色
   - 可以正常使用所有功能
   - 点击"登出"安全退出

3. **会话管理**：
   - 登录状态会保持24小时
   - 勾选"记住我"可以延长会话
   - 关闭浏览器后重新打开仍保持登录状态

## 故障排除

### 问题：PowerShell 执行策略错误

**解决方案：**
1. 以管理员身份运行 PowerShell
2. 执行：`Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### 问题：端口 3000 被占用

**解决方案：**
1. 修改 `server.js` 中的端口号
2. 或者关闭占用端口的程序

### 问题：Node.js 未安装

**解决方案：**
1. 访问 https://nodejs.org/
2. 下载并安装 Node.js LTS 版本

### 问题：登录失败

**解决方案：**
1. 检查用户名和密码是否正确
2. 确认 `src/users.json` 文件存在
3. 查看浏览器控制台错误信息

## 开发说明

如果需要开发模式，可以使用 Webpack Dev Server：

```bash
# 安装依赖（如果遇到 npm 问题，请先解决 PowerShell 执行策略）
npm install

# 启动开发服务器
npm run dev
```

## 安全说明

- 本项目为演示用途，密码采用明文存储
- 生产环境请使用加密存储和更安全的认证机制
- Token 生成算法较简单，生产环境建议使用 JWT

## 许可证

ISC License
