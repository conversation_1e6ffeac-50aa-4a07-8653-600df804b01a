{"version": 3, "file": "encode.js", "sourceRoot": "", "sources": ["../../src/utf8/encode.ts"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,SAAS,CAAC;AAC9C,MAAM,SAAS,GAAG,SAAS;IACzB,CAAC,CAAE,MAAM,CAAC,SAAS,CAAC,SAAuF;IAC3G,CAAC,CAAC,IAAI,CAAC;AACT,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAIrC,MAAM,eAAe,GAAiB,CAAC,GAAe,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB,EAAU,EAAE,CACpH,SAAU,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAD/B,QAAA,eAAe,mBACgB;AAErC,MAAM,UAAU,GAAiB,CAAC,GAAe,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB,EAAU,EAAE;IAC/G,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC;IACpC,MAAM,GAAG,GAAG,IAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC;IACnE,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC,CAAC;AAJW,QAAA,UAAU,cAIrB;AAEK,MAAM,YAAY,GAAiB,CAAC,GAAe,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB,EAAU,EAAE;IACjH,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,KAAK,GAAG,GAAG,CAAC;IAClB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,OAAO,IAAI,GAAG,MAAM,EAAE,CAAC;QACrB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;YACnB,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;gBACvC,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;oBAClB,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACnC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;wBAChC,IAAI,EAAE,CAAC;wBACP,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC3C,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC3C,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC3C,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAC5C,CAAC;QACH,CAAC;QACD,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IACrC,CAAC;IACD,OAAO,GAAG,GAAG,KAAK,CAAC;AACrB,CAAC,CAAC;AAjCW,QAAA,YAAY,gBAiCvB;AAEF,MAAM,WAAW,GAAG,OAAO,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;AAE3E,MAAM,QAAQ,GAAiB,CAAC,GAAe,EAAE,GAAW,EAAE,GAAW,EAAE,SAAiB,EAAU,EAAE,CAC7G,WAAY,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,OAAQ,CAAC;AAD/D,QAAA,QAAQ,YACuD;AAE/D,QAAA,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,uBAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAU,CAAC,CAAC,CAAC,oBAAY,CAAC"}