<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 多页面管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #6a11cb;
            background: white;
            box-shadow: 0 0 0 3px rgba(106, 17, 203, 0.1);
        }

        .form-group .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(106, 17, 203, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .remember-me input {
            margin-right: 8px;
        }

        .forgot-password {
            color: #6a11cb;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #2575fc;
        }

        .demo-accounts {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .demo-accounts h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e1e8ed;
            font-size: 13px;
        }

        .demo-account:last-child {
            border-bottom: none;
        }

        .demo-account .role {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
        }

        .demo-account .role.admin {
            background: #fff3e0;
            color: #f57c00;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .back-to-home {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-home a {
            color: #6a11cb;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .back-to-home a:hover {
            color: #2575fc;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 用户登录</h1>
            <p>欢迎回到多页面管理系统</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="error-message"></div>
            <div class="success-message" id="success-message"></div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                    <span class="input-icon">👤</span>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                    <span class="input-icon">🔒</span>
                </div>
                
                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" id="remember-me">
                        记住我
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn" id="login-btn">
                    <span id="btn-text">登录</span>
                    <span class="loading" id="loading" style="display: none;"></span>
                </button>
            </form>
            
            <div class="demo-accounts">
                <h4>📋 演示账户</h4>
                <div class="demo-account">
                    <span>admin / 123456</span>
                    <span class="role admin">管理员</span>
                </div>
                <div class="demo-account">
                    <span>user / 123456</span>
                    <span class="role">普通用户</span>
                </div>
                <div class="demo-account">
                    <span>test / test123</span>
                    <span class="role">测试用户</span>
                </div>
            </div>
            
            <div class="back-to-home">
                <a href="/index.html">← 返回首页</a>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        function checkLoginStatus() {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');
            
            if (token && userData) {
                // 验证token是否有效
                fetch('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 已登录，重定向到首页
                        showSuccessMessage('您已登录，正在跳转到首页...');
                        setTimeout(() => {
                            window.location.href = '/index.html';
                        }, 1500);
                    }
                })
                .catch(error => {
                    console.log('Token验证失败，需要重新登录');
                });
            }
        }

        // 登录表单提交
        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember-me').checked;
            
            if (!username || !password) {
                showErrorMessage('请输入用户名和密码');
                return;
            }
            
            // 显示加载状态
            setLoadingState(true);
            hideMessages();
            
            // 发送登录请求
            fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    rememberMe: rememberMe
                })
            })
            .then(response => response.json())
            .then(data => {
                setLoadingState(false);
                
                if (data.success) {
                    // 登录成功
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userData', JSON.stringify(data.user));
                    
                    if (rememberMe) {
                        localStorage.setItem('rememberLogin', 'true');
                    }
                    
                    showSuccessMessage(`登录成功！欢迎回来，${data.user.username}`);
                    
                    // 延迟跳转到首页
                    setTimeout(() => {
                        window.location.href = '/index.html';
                    }, 1500);
                } else {
                    // 登录失败
                    showErrorMessage(data.message || '登录失败，请检查用户名和密码');
                }
            })
            .catch(error => {
                setLoadingState(false);
                console.error('登录请求失败:', error);
                showErrorMessage('网络错误，请稍后重试');
            });
        });

        // 设置加载状态
        function setLoadingState(loading) {
            const btn = document.getElementById('login-btn');
            const btnText = document.getElementById('btn-text');
            const loadingIcon = document.getElementById('loading');
            
            if (loading) {
                btn.disabled = true;
                btnText.style.display = 'none';
                loadingIcon.style.display = 'inline-block';
            } else {
                btn.disabled = false;
                btnText.style.display = 'inline';
                loadingIcon.style.display = 'none';
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        // 演示账户快速填充
        document.querySelectorAll('.demo-account').forEach(account => {
            account.addEventListener('click', function() {
                const text = this.querySelector('span').textContent;
                const [username, password] = text.split(' / ');
                
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
                
                // 添加点击效果
                this.style.background = '#e3f2fd';
                setTimeout(() => {
                    this.style.background = '';
                }, 200);
            });
        });

        // 回车键快速登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-form').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
