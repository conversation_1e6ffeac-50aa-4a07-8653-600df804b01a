<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 多页面管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #6a11cb;
            background: white;
            box-shadow: 0 0 0 3px rgba(106, 17, 203, 0.1);
        }

        .form-group .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(106, 17, 203, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #666;
        }

        .remember-me input {
            margin-right: 8px;
        }

        .forgot-password {
            color: #6a11cb;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #2575fc;
        }

        .demo-accounts {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .demo-accounts h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e1e8ed;
            font-size: 13px;
        }

        .demo-account:last-child {
            border-bottom: none;
        }

        .demo-account .role {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
        }

        .demo-account .role.admin {
            background: #fff3e0;
            color: #f57c00;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .back-to-home {
            text-align: center;
            margin-top: 20px;
        }

        .back-to-home a {
            color: #6a11cb;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .back-to-home a:hover {
            color: #2575fc;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 用户登录</h1>
            <p>欢迎回到多页面管理系统</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="error-message"></div>
            <div class="success-message" id="success-message"></div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                    <span class="input-icon">👤</span>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                    <span class="input-icon">🔒</span>
                </div>
                
                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" id="remember-me">
                        记住我
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn" id="login-btn">
                    <span id="btn-text">登录</span>
                    <span class="loading" id="loading" style="display: none;"></span>
                </button>
            </form>
            
            <div class="demo-accounts">
                <h4>📋 演示账户</h4>
                <div class="demo-account">
                    <span>admin / 123456</span>
                    <span class="role admin">管理员</span>
                </div>
                <div class="demo-account">
                    <span>user / 123456</span>
                    <span class="role">普通用户</span>
                </div>
                <div class="demo-account">
                    <span>test / test123</span>
                    <span class="role">测试用户</span>
                </div>
            </div>
            
            <div class="back-to-home">
                <a href="/index.html">← 返回首页</a>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginPage();
        });

        // 初始化登录页面
        async function initializeLoginPage() {
            console.log('初始化登录页面...');

            // 检查登录状态
            await checkLoginStatus();

            // 设置表单实时验证
            setupFormValidation();

            // 设置快捷键
            setupKeyboardShortcuts();

            // 预填充记住的用户名
            loadRememberedUsername();

            console.log('登录页面初始化完成');
        }

        // 设置表单实时验证
        function setupFormValidation() {
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');

            // 用户名实时验证（防抖）
            usernameInput.addEventListener('input', debounce(function() {
                validateUsernameAsync(this.value.trim());
            }, 500));

            // 密码实时验证
            passwordInput.addEventListener('input', debounce(function() {
                validatePasswordAsync(this.value);
            }, 300));

            // 失去焦点时验证
            usernameInput.addEventListener('blur', function() {
                validateUsernameAsync(this.value.trim());
            });

            passwordInput.addEventListener('blur', function() {
                validatePasswordAsync(this.value);
            });
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func.apply(this, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 异步验证用户名
        async function validateUsernameAsync(username) {
            const usernameInput = document.getElementById('username');

            // 清除之前的样式
            usernameInput.style.borderColor = '';

            if (!username) {
                return;
            }

            if (username.length < 2) {
                usernameInput.style.borderColor = '#e74c3c';
                return;
            }

            // 模拟检查用户名是否存在（可选功能）
            try {
                // 这里可以添加检查用户名是否存在的API调用
                usernameInput.style.borderColor = '#27ae60';
            } catch (error) {
                console.log('用户名验证失败:', error);
            }
        }

        // 异步验证密码
        async function validatePasswordAsync(password) {
            const passwordInput = document.getElementById('password');

            // 清除之前的样式
            passwordInput.style.borderColor = '';

            if (!password) {
                return;
            }

            if (password.length < 3) {
                passwordInput.style.borderColor = '#e74c3c';
                return;
            }

            passwordInput.style.borderColor = '#27ae60';
        }

        // 设置键盘快捷键
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+Enter 快速登录
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('login-form').dispatchEvent(new Event('submit'));
                }

                // ESC 清除表单
                if (e.key === 'Escape') {
                    clearForm();
                }
            });
        }

        // 加载记住的用户名
        function loadRememberedUsername() {
            const rememberLogin = localStorage.getItem('rememberLogin');
            const userData = localStorage.getItem('userData');

            if (rememberLogin === 'true' && userData) {
                try {
                    const user = JSON.parse(userData);
                    document.getElementById('username').value = user.username;
                    document.getElementById('remember-me').checked = true;
                } catch (error) {
                    console.log('加载记住的用户名失败:', error);
                }
            }
        }

        // 清除表单
        function clearForm() {
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('remember-me').checked = false;
            hideMessages();

            // 重置输入框样式
            document.getElementById('username').style.borderColor = '';
            document.getElementById('password').style.borderColor = '';
        }

        // 检查登录状态（异步）
        async function checkLoginStatus() {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');
            const loginTime = localStorage.getItem('loginTime');

            if (!token || !userData) {
                console.log('未找到登录信息');
                return;
            }

            // 检查登录时间是否过期（24小时）
            if (loginTime) {
                const now = Date.now();
                const loginTimestamp = parseInt(loginTime);
                const hoursPassed = (now - loginTimestamp) / (1000 * 60 * 60);

                if (hoursPassed > 24) {
                    console.log('登录已过期，清除本地数据');
                    clearLoginData();
                    return;
                }
            }

            try {
                console.log('验证现有登录状态...');

                // 异步验证token是否有效
                const response = await fetchWithTimeout('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                }, 5000);

                if (!response.ok) {
                    throw new Error(`验证失败: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    // 已登录，重定向到首页
                    console.log('用户已登录，准备跳转');
                    showSuccessMessage('您已登录，正在跳转到首页...');

                    setTimeout(() => {
                        const redirectUrl = getRedirectUrl() || '/index.html';
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    console.log('Token无效，清除登录数据');
                    clearLoginData();
                }

            } catch (error) {
                console.log('Token验证失败:', error.message);
                // 网络错误时不清除数据，允许用户重新尝试登录
                if (!error.message.includes('验证失败')) {
                    console.log('网络错误，保留登录数据');
                } else {
                    clearLoginData();
                }
            }
        }

        // 清除登录数据
        function clearLoginData() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
            localStorage.removeItem('loginTime');
            localStorage.removeItem('rememberLogin');
            console.log('已清除所有登录数据');
        }

        // 登录表单提交
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember-me').checked;

            // 表单验证
            if (!validateLoginForm(username, password)) {
                return;
            }

            // 执行登录（包含网络检查）
            await performLoginWithNetworkCheck(username, password, rememberMe);
        });

        // 表单验证函数
        function validateLoginForm(username, password) {
            hideMessages();

            if (!username || !password) {
                showErrorMessage('请输入用户名和密码');
                return false;
            }

            if (username.length < 2) {
                showErrorMessage('用户名至少需要2个字符');
                return false;
            }

            if (password.length < 3) {
                showErrorMessage('密码至少需要3个字符');
                return false;
            }

            return true;
        }

        // 执行登录的异步函数
        async function performLogin(username, password, rememberMe, retryCount = 0) {
            const maxRetries = 2;

            try {
                // 显示加载状态
                setLoadingState(true);

                // 创建请求数据
                const loginData = {
                    username: username,
                    password: password,
                    rememberMe: rememberMe,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent
                };

                console.log('发送登录请求:', { username, rememberMe });

                // 发送异步登录请求
                const response = await fetchWithTimeout('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                }, 10000); // 10秒超时

                // 检查响应状态
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 解析响应数据
                const data = await response.json();
                console.log('登录响应:', data);

                // 处理登录结果
                await handleLoginResponse(data, username);

            } catch (error) {
                console.error('登录请求失败:', error);

                // 处理不同类型的错误
                if (error.name === 'TimeoutError') {
                    if (retryCount < maxRetries) {
                        showErrorMessage(`请求超时，正在重试... (${retryCount + 1}/${maxRetries + 1})`);
                        setTimeout(() => {
                            performLogin(username, password, rememberMe, retryCount + 1);
                        }, 2000);
                        return;
                    } else {
                        showErrorMessage('请求超时，请检查网络连接后重试');
                    }
                } else if (error.message.includes('Failed to fetch')) {
                    showErrorMessage('网络连接失败，请检查网络设置');
                } else if (error.message.includes('HTTP 401')) {
                    showErrorMessage('用户名或密码错误');
                } else if (error.message.includes('HTTP 429')) {
                    showErrorMessage('请求过于频繁，请稍后再试');
                } else if (error.message.includes('HTTP 500')) {
                    showErrorMessage('服务器内部错误，请稍后重试');
                } else {
                    showErrorMessage(error.message || '登录失败，请重试');
                }
            } finally {
                setLoadingState(false);
            }
        }

        // 带超时的fetch函数
        async function fetchWithTimeout(url, options, timeout = 8000) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            try {
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                return response;
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    const timeoutError = new Error('请求超时');
                    timeoutError.name = 'TimeoutError';
                    throw timeoutError;
                }
                throw error;
            }
        }

        // 处理登录响应
        async function handleLoginResponse(data, username) {
            if (data.success) {
                // 登录成功
                console.log('登录成功，保存用户数据');

                // 保存认证信息到本地存储
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('userData', JSON.stringify(data.user));
                localStorage.setItem('loginTime', Date.now().toString());

                if (document.getElementById('remember-me').checked) {
                    localStorage.setItem('rememberLogin', 'true');
                }

                // 显示成功消息
                showSuccessMessage(`登录成功！欢迎回来，${data.user.username}`);

                // 触发登录成功事件
                window.dispatchEvent(new CustomEvent('userLoggedIn', {
                    detail: { user: data.user, token: data.token }
                }));

                // 延迟跳转到首页
                setTimeout(() => {
                    const redirectUrl = getRedirectUrl() || '/index.html';
                    window.location.href = redirectUrl;
                }, 1500);

            } else {
                // 登录失败
                console.log('登录失败:', data.message);
                showErrorMessage(data.message || '登录失败，请检查用户名和密码');

                // 清除可能存在的旧数据
                localStorage.removeItem('authToken');
                localStorage.removeItem('userData');
            }
        }

        // 获取重定向URL
        function getRedirectUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const redirect = urlParams.get('redirect');
            return redirect && redirect.startsWith('/') ? redirect : null;
        }

        // 网络状态监控
        let isOnline = navigator.onLine;
        let networkStatusIndicator = null;

        // 监听网络状态变化
        window.addEventListener('online', function() {
            isOnline = true;
            hideNetworkStatus();
            console.log('网络已连接');
        });

        window.addEventListener('offline', function() {
            isOnline = false;
            showNetworkStatus('网络连接已断开，请检查网络设置', 'error');
            console.log('网络已断开');
        });

        // 显示网络状态
        function showNetworkStatus(message, type = 'info') {
            hideNetworkStatus();

            networkStatusIndicator = document.createElement('div');
            networkStatusIndicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
                ${type === 'error' ? 'background: #e74c3c;' : 'background: #3498db;'}
            `;
            networkStatusIndicator.textContent = message;

            document.body.appendChild(networkStatusIndicator);
        }

        // 隐藏网络状态
        function hideNetworkStatus() {
            if (networkStatusIndicator) {
                networkStatusIndicator.remove();
                networkStatusIndicator = null;
            }
        }

        // 检查网络连接
        async function checkNetworkConnection() {
            if (!isOnline) {
                return false;
            }

            try {
                const response = await fetch('/api/health', {
                    method: 'GET',
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(3000)
                });
                return response.ok;
            } catch (error) {
                console.log('网络连接检查失败:', error);
                return false;
            }
        }

        // 增强的异步登录函数（包含网络检查）
        async function performLoginWithNetworkCheck(username, password, rememberMe) {
            // 检查网络连接
            if (!isOnline) {
                showErrorMessage('网络连接已断开，请检查网络设置');
                return;
            }

            const networkOk = await checkNetworkConnection();
            if (!networkOk) {
                showErrorMessage('无法连接到服务器，请检查网络连接');
                return;
            }

            // 执行登录
            await performLogin(username, password, rememberMe);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .input-validation-success {
                border-color: #27ae60 !important;
                box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
            }

            .input-validation-error {
                border-color: #e74c3c !important;
                box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
            }
        `;
        document.head.appendChild(style);

        // 设置加载状态
        function setLoadingState(loading) {
            const btn = document.getElementById('login-btn');
            const btnText = document.getElementById('btn-text');
            const loadingIcon = document.getElementById('loading');
            
            if (loading) {
                btn.disabled = true;
                btnText.style.display = 'none';
                loadingIcon.style.display = 'inline-block';
            } else {
                btn.disabled = false;
                btnText.style.display = 'inline';
                loadingIcon.style.display = 'none';
            }
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        // 演示账户快速填充
        document.querySelectorAll('.demo-account').forEach(account => {
            account.addEventListener('click', function() {
                const text = this.querySelector('span').textContent;
                const [username, password] = text.split(' / ');
                
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
                
                // 添加点击效果
                this.style.background = '#e3f2fd';
                setTimeout(() => {
                    this.style.background = '';
                }, 200);
            });
        });

        // 回车键快速登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-form').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
